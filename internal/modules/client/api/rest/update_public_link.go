package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type publicClientLinkUpdate struct {
	TurnID    string   `json:"turn_id" validate:"required"`
	WorkerIDs []string `json:"worker_ids" validate:"required"`
	ClientID  string   `json:"client_id" validate:"required"`
	URL       string   `json:"url" validate:"required"`
}

func publicClientLinkUpdateToModel(id string, req publicClientLinkUpdate) model.PublicClientLinkUpdate {
	return model.PublicClientLinkUpdate{
		ID:        id,
		TurnID:    req.TurnID,
		WorkerIDs: req.WorkerIDs,
		ClientID:  req.ClientID,
		URL:       req.URL,
	}
}

// UpdatePublicLink implements ClientHandler.
func (c *clientHandler) UpdatePublicLink(w http.ResponseWriter, r *http.Request) {
	id := r.PathValue("id")
	ctx := r.Context()

	req, err := rest.DecodeBody[publicClientLinkUpdate](w, r)
	if err != nil {
		rest.ErrorResponse(w, r, utils.ParseErrorf("failed to decode request body", err, nil), http.StatusBadRequest)
		return
	}

	err = c.useCase.UpdatePublicLink(ctx, publicClientLinkUpdateToModel(id, *req))
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to update public client link")
		return
	}

	rest.SuccessResponse(w, r, http.StatusOK)
}
