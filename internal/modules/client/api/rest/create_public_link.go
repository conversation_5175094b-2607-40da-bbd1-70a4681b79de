package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/rest"
)

type publicClientLinkCreate struct {
	TurnID    string   `json:"turn_id" validate:"required"`
	WorkerIDs []string `json:"worker_ids" validate:"required"`
	ClientID  string   `json:"client_id" validate:"required"`
	URL       string   `json:"url" validate:"required"`
}

func publicClientLinkCreateToModel(req publicClientLinkCreate) model.PublicClientLinkCreate {
	return model.PublicClientLinkCreate{
		TurnID:    req.TurnID,
		WorkerIDs: req.WorkerIDs,
		ClientID:  req.ClientID,
		URL:       req.URL,
	}
}

// CreatePublicLink implements ClientHandler.
func (c *clientHandler) CreatePublicLink(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeBody[publicClientLinkCreate](w, r)
	if err != nil {
		rest.ErrorResponse(w, r, utils.ParseErrorf("failed to decode request body", err, nil), http.StatusBadRequest)
		return
	}

	err = c.useCase.CreatePublicLink(ctx, publicClientLinkCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to post public client link")
		return
	}

	rest.SuccessResponse(w, r, http.StatusCreated)
}
