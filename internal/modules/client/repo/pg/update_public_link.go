package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) UpdatePublicLink(ctx context.Context, link model.PublicClientLink) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            UPDATE public_client_links
            SET turn_id = $2, worker_ids = $3, client_id = $4, url = $5, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
        `

		result, err := conn.Exec(ctx, query,
			link.ID,
			link.TurnID,
			link.WorkerIDs,
			link.ClientID,
			link.URL,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update public client link", err, nil)
		}

		if result.RowsAffected() == 0 {
			return utils.NotFoundf(
				fmt.Sprintf("public client link with id: %s not found", link.ID),
				fmt.Errorf("public client link not found or already deleted"),
				nil,
			)
		}

		return nil
	})
}
