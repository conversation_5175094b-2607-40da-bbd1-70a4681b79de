package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) CreatePublicLink(ctx context.Context, link model.PublicClientLink) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            INSERT INTO public_client_links (id, turn_id, worker_ids, client_id, url)
            VALUES ($1, $2, $3, $4, $5)
        `

		_, err := conn.Exec(ctx, query,
			link.ID,
			link.TurnID,
			link.WorkerIDs,
			link.ClientID,
			link.URL,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create public client link", err, nil)
		}

		return nil
	})
}
