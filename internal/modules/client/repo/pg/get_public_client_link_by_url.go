package pg

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) GetPublicClientLinkByURL(ctx context.Context, url string) (*model.PublicClientLink, error) {
	var link model.PublicClientLink

	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT id, turn_id, worker_ids, client_id, url, created_at, updated_at, deleted_at
            FROM public_client_links
            WHERE url = $1 AND deleted_at IS NULL
        `

		row := conn.QueryRow(ctx, query, url)
		err := row.Scan(
			&link.ID,
			&link.TurnID,
			&link.WorkerIDs,
			&link.ClientID,
			&link.URL,
			&link.CreatedAt,
			&link.UpdatedAt,
			&link.DeletedAt,
		)

		if err != nil {
			return utils.NotFoundf(
				"public client link not found",
				err,
				nil,
			)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &link, nil
}
